<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    破局的"支点"与行动的"地图"
  </text>
  
  <!-- 破局的支点 -->
  <text x="120" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">破局的"支点"</text>
  <rect x="120" y="220" width="140" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="280" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    我跟孙经理分享了一个理念："当主食已经吃到顶的时候，我们应该开始推荐'餐后甜点'和'年份红酒'了。"
  </text>
  <text x="120" y="320" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    破局的关键，在于将目光从"通信套餐"本身，转移到客户的"数字化生活场景"中去，
  </text>
  <text x="120" y="360" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    用精准的"场景服务"，去创造新的价值锚点。
  </text>
  
  <!-- 行动地图 -->
  <text x="120" y="460" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">行动的"地图"</text>
  <rect x="120" y="480" width="140" height="4" fill="#2E5BBA"/>
  
  <!-- 第一步 -->
  <rect x="120" y="540" width="1680" height="100" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="580" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第一步：用"行为"代替"标签"，洞察场景需求
  </text>
  <text x="150" y="620" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    通过数据，去发现他们独特的"行为模式"，并以此判断他们所处的"场景"
  </text>
  
  <!-- 三个场景 -->
  <rect x="120" y="680" width="520" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">场景一："空中飞人"</text>
  <text x="140" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">行为特征：</text>
  <text x="140" y="790" font-family="Microsoft YaHei" font-size="18" fill="#333333">每月都有国际漫游记录，且话费账单中，</text>
  <text x="140" y="820" font-family="Microsoft YaHei" font-size="18" fill="#333333">航旅类消费较高</text>
  
  <text x="140" y="870" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">场景二："数字娱乐家"</text>
  <text x="140" y="910" font-family="Microsoft YaHei" font-size="18" fill="#333333">每月流量消耗巨大，且大部分都流向了</text>
  <text x="140" y="940" font-family="Microsoft YaHei" font-size="18" fill="#333333">主流的视频、音乐、游戏APP</text>
  
  <rect x="680" y="680" width="520" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="700" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">场景三："数码发烧友"</text>
  <text x="700" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">行为特征：</text>
  <text x="700" y="790" font-family="Microsoft YaHei" font-size="18" fill="#333333">名下有多张副卡，且账单中，</text>
  <text x="700" y="820" font-family="Microsoft YaHei" font-size="18" fill="#333333">应用商店的消费额度很高</text>
  
  <!-- 第二步预告 -->
  <rect x="1240" y="680" width="520" height="280" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1260" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">第二步预告</text>
  <text x="1260" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">设计"触发式"的</text>
  <text x="1260" y="790" font-family="Microsoft YaHei" font-size="20" fill="#333333">场景化服务</text>
  <text x="1260" y="840" font-family="Microsoft YaHei" font-size="18" fill="#666666">为每一个场景，都设计一个</text>
  <text x="1260" y="870" font-family="Microsoft YaHei" font-size="18" fill="#666666">"非侵入式"的、在特定时机</text>
  <text x="1260" y="900" font-family="Microsoft YaHei" font-size="18" fill="#666666">触发的增值服务推荐</text>
</svg>
