<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    技术武装与话术升级
  </text>
  
  <!-- 第二步 -->
  <rect x="120" y="160" width="1680" height="100" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="200" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第二步：技术武装——让系统成为"智能参谋"
  </text>
  <text x="150" y="240" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    在客服代表的工作后台，植入了一个"智能推荐"模块
  </text>
  
  <!-- 场景示例 -->
  <rect x="120" y="300" width="1680" height="200" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="150" y="340" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">场景示例：</text>
  <text x="150" y="380" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    一位客户，连续三个月，因为"流量超额"而来电咨询。
  </text>
  <text x="150" y="420" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    系统自动推送："高概率机会点：流量升级包。参考话术：'王先生，您的问题我已经为您解决了。
  </text>
  <text x="150" y="450" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    另外我看到，您最近每个月流量似乎都有些紧张。其实我们有一个……'"
  </text>
  <text x="150" y="480" font-family="Microsoft YaHei" font-size="18" fill="#666666">
    当客户电话接通时，系统会根据客户的画像和本次来电意图，自动推送最有可能成交的增值业务
  </text>
  
  <!-- 第三步 -->
  <rect x="120" y="540" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="580" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第三步：话术升级——从"解答者"到"咨询顾问"
  </text>
  <text x="150" y="620" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    对所有客服代表，进行了一轮全新的沟通技巧培训
  </text>
  
  <!-- 核心原则和标准流程 -->
  <rect x="120" y="700" width="800" height="200" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="740" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">核心原则</text>
  <text x="140" y="780" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    "先解决情感，再解决问题；
  </text>
  <text x="140" y="810" font-family="Microsoft YaHei" font-size="20" fill="#333333">
    先处理当下，再规划未来。"
  </text>
  <text x="140" y="860" font-family="Microsoft YaHei" font-size="18" fill="#666666">
    让客服代表从被动的"解答者"
  </text>
  <text x="140" y="890" font-family="Microsoft YaHei" font-size="18" fill="#666666">
    转变为主动的"咨询顾问"
  </text>
  
  <rect x="1000" y="700" width="800" height="200" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1020" y="740" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">标准流程</text>
  <text x="1020" y="780" font-family="Microsoft YaHei" font-size="18" fill="#333333">
    在每一次通话的结尾，在确认客户的问题，
  </text>
  <text x="1020" y="810" font-family="Microsoft YaHei" font-size="18" fill="#333333">
    已经得到圆满解决后，客服代表都必须，
  </text>
  <text x="1020" y="840" font-family="Microsoft YaHei" font-size="18" fill="#333333">
    自然地，加上一句："先生/女士，为了确保您
  </text>
  <text x="1020" y="870" font-family="Microsoft YaHei" font-size="18" fill="#333333">
    未来不再遇到类似的问题，我可否占用您一分钟，
  </text>
  <text x="1020" y="900" font-family="Microsoft YaHei" font-size="18" fill="#333333">
    为您提供一个可能更适合您的优化建议？"
  </text>
  
  <!-- 核心理念 -->
  <rect x="300" y="950" width="1320" height="80" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="960" y="1000" text-anchor="middle" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">
    让每一次服务，都成为一次创造新价值的开始
  </text>
</svg>
