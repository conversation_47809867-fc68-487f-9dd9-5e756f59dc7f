<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    "纯粹"的社群运营与"无意"的转化
  </text>
  
  <!-- 第二步 -->
  <rect x="120" y="160" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="200" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第二步：做一个"纯粹"的社群，克制营销
  </text>
  <text x="150" y="240" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    我们建立了一个微信群，并立下铁规：群内，绝不主动谈论任何通信业务
  </text>
  
  <!-- 社群运营内容 -->
  <rect x="120" y="320" width="800" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="360" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">每日内容</text>
  <text x="140" y="400" font-family="Microsoft YaHei" font-size="18" fill="#333333">助理每天，会在群里分享：</text>
  <text x="140" y="430" font-family="Microsoft YaHei" font-size="18" fill="#333333">• "周边美食探店报告"</text>
  <text x="140" y="460" font-family="Microsoft YaHei" font-size="18" fill="#333333">• "下午茶优惠团购"</text>
  <text x="140" y="490" font-family="Microsoft YaHei" font-size="18" fill="#333333">• "下班后最新电影推荐"</text>
  <text x="140" y="520" font-family="Microsoft YaHei" font-size="18" fill="#333333">• "免费的职场讲座信息"等</text>
  <text x="140" y="570" font-family="Microsoft YaHei" font-size="16" fill="#666666">纯粹为白领提供生活便利</text>
  
  <rect x="1000" y="320" width="800" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1020" y="360" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">线下据点</text>
  <text x="1020" y="400" font-family="Microsoft YaHei" font-size="18" fill="#333333">我们的营业厅，则变成了这个社群的</text>
  <text x="1020" y="430" font-family="Microsoft YaHei" font-size="18" fill="#333333">"线下会客厅"。群成员，可以随时来这里：</text>
  <text x="1020" y="470" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 免费充电</text>
  <text x="1020" y="500" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 免费打印文件</text>
  <text x="1020" y="530" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 免费寄存物品</text>
  <text x="1020" y="570" font-family="Microsoft YaHei" font-size="16" fill="#666666">营业厅变身社交中心</text>
  
  <!-- 第三步 -->
  <rect x="120" y="640" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="680" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第三步：让"转化"，在"无意"中发生
  </text>
  <text x="150" y="720" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    我们只在两个场景，进行"克制"的转化
  </text>
  
  <!-- 两个转化场景 -->
  <rect x="120" y="800" width="800" height="200" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="840" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#2E5BBA">场景一：价值植入</text>
  <text x="140" y="880" font-family="Microsoft YaHei" font-size="18" fill="#333333">比如，在分享一个"周边书店"时，我们会</text>
  <text x="140" y="910" font-family="Microsoft YaHei" font-size="18" fill="#333333">顺便提一句："这家书店的角落里，我们亲测</text>
  <text x="140" y="940" font-family="Microsoft YaHei" font-size="18" fill="#333333">5G信号满格，很适合移动办公哦。"</text>
  <text x="140" y="970" font-family="Microsoft YaHei" font-size="16" fill="#666666">自然而然地展示专业价值</text>
  
  <rect x="1000" y="800" width="800" height="200" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1020" y="840" font-family="Microsoft YaHei" font-size="22" font-weight="bold" fill="#2E5BBA">场景二：需求响应</text>
  <text x="1020" y="880" font-family="Microsoft YaHei" font-size="18" fill="#333333">当群里有人抱怨"办公室网速太慢"时，</text>
  <text x="1020" y="910" font-family="Microsoft YaHei" font-size="18" fill="#333333">李经理会以"群主"和"专家"的身份，主动@他：</text>
  <text x="1020" y="940" font-family="Microsoft YaHei" font-size="18" fill="#333333">"张先生，您这个问题，我比较专业。您方便</text>
  <text x="1020" y="970" font-family="Microsoft YaHei" font-size="18" fill="#333333">的时候，可以来我们'会客厅'坐坐，我帮您免费做个诊断。"</text>
</svg>
