<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    破局的"支点"与行动的"地图"
  </text>
  
  <!-- 破局的支点 -->
  <text x="120" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">破局的"支点"</text>
  <rect x="120" y="220" width="140" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="280" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    我向钱总提了一个问题："用户需要的，真的是一个'摄像头'或一个'门锁'吗？"
  </text>
  <text x="120" y="320" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    我们深入探讨后发现，用户需要的，从来都不是设备本身，而是设备所能解决的"生活问题"。
  </text>
  <text x="120" y="360" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    破局的关键，在于停止销售孤立的"产品"，转而开始提供打包的"家庭场景解决方案"。
  </text>
  
  <!-- 行动地图 -->
  <text x="120" y="460" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">行动的"地图"</text>
  <rect x="120" y="480" width="140" height="4" fill="#2E5BBA"/>
  
  <!-- 第一步 -->
  <rect x="120" y="540" width="1680" height="100" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="580" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第一步：从"功能"到"场景"的重新组合
  </text>
  <text x="150" y="620" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    不再单独售卖设备，而是根据典型的家庭需求，设计了三个"生活方式包"
  </text>
  
  <!-- 三个生活方式包 -->
  <rect x="120" y="680" width="520" height="320" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"安心守护包"</text>
  <text x="140" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">面向家里有老人或小孩的家庭</text>
  <text x="140" y="800" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能摄像头</text>
  <text x="140" y="830" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能门锁</text>
  <text x="140" y="860" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 烟雾报警器</text>
  <text x="140" y="890" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 全年的云存储服务</text>
  
  <rect x="680" y="680" width="520" height="320" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="700" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"懒人生活包"</text>
  <text x="700" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">面向追求生活品质的年轻用户</text>
  <text x="700" y="800" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能音箱</text>
  <text x="700" y="830" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 全屋智能灯光</text>
  <text x="700" y="860" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能窗帘</text>
  <text x="700" y="890" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能插座</text>
  
  <rect x="1240" y="680" width="520" height="320" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1260" y="720" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"健康关怀包"</text>
  <text x="1260" y="760" font-family="Microsoft YaHei" font-size="20" fill="#333333">面向需要照顾长辈的家庭</text>
  <text x="1260" y="800" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能血压计</text>
  <text x="1260" y="830" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 紧急呼叫按钮</text>
  <text x="1260" y="860" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 智能药盒</text>
  <text x="1260" y="890" font-family="Microsoft YaHei" font-size="18" fill="#333333">• 家庭健康APP</text>
</svg>
