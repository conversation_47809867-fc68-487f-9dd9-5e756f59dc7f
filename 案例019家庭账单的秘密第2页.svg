<?xml version="1.0" encoding="UTF-8"?>
<svg width="1920" height="1080" viewBox="0 0 1920 1080" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="#FFFFFF"/>
  
  <!-- 标题区域 -->
  <rect x="0" y="0" width="1920" height="120" fill="#2E5BBA" opacity="0.1"/>
  
  <!-- 页面标题 -->
  <text x="960" y="70" text-anchor="middle" font-family="Microsoft YaHei" font-size="36" font-weight="bold" fill="#2E5BBA">
    破局的"支点"与行动的"地图"
  </text>
  
  <!-- 破局的支点 -->
  <text x="120" y="200" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">破局的"支点"</text>
  <rect x="120" y="220" width="140" height="4" fill="#2E5BBA"/>
  
  <text x="120" y="280" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    我建议小陈，停止"盲目的推销"，而是去做一次"数据侦探"。我告诉他："一个家庭的通信账单，
  </text>
  <text x="120" y="320" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    就是一部关于他们生活方式的'连续剧'。"
  </text>
  <text x="120" y="360" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    我们的破局点，就是（在合规和用户授权的前提下）学会阅读"家庭账单"这部连续剧，
  </text>
  <text x="120" y="400" font-family="Microsoft YaHei" font-size="24" fill="#333333">
    从中发现"剧情"的变化，并据此，预测他们下一步的需求。
  </text>
  
  <!-- 行动地图 -->
  <text x="120" y="500" font-family="Microsoft YaHei" font-size="32" font-weight="bold" fill="#2E5BBA">行动的"地图"</text>
  <rect x="120" y="520" width="140" height="4" fill="#2E5BBA"/>
  
  <!-- 第一步 -->
  <rect x="120" y="580" width="1680" height="120" fill="#F8F9FA" stroke="#2E5BBA" stroke-width="2"/>
  <text x="150" y="620" font-family="Microsoft YaHei" font-size="28" font-weight="bold" fill="#2E5BBA">
    第一步：从"账单"中，读出"故事"
  </text>
  <text x="150" y="660" font-family="Microsoft YaHei" font-size="22" fill="#333333">
    放弃了过去只看"总消费额"的粗放式分析，用新的方法，去交叉分析一个家庭账户下，所有号码的行为模式
  </text>
  
  <!-- 三个家庭故事模式 -->
  <rect x="120" y="740" width="520" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="140" y="780" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"空巢之爱"模式</text>
  <text x="140" y="820" font-family="Microsoft YaHei" font-size="20" fill="#333333">主卡户主年龄大于55岁，通话记录中，</text>
  <text x="140" y="850" font-family="Microsoft YaHei" font-size="20" fill="#333333">频繁拨打一个外地的、数据流量消耗</text>
  <text x="140" y="880" font-family="Microsoft YaHei" font-size="20" fill="#333333">巨大的号码。</text>
  <text x="140" y="920" font-family="Microsoft YaHei" font-size="18" fill="#666666">推断：这是子女在外地工作的</text>
  <text x="140" y="950" font-family="Microsoft YaHei" font-size="18" fill="#666666">"空巢"家庭</text>
  
  <rect x="680" y="740" width="520" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="700" y="780" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"学童初长"模式</text>
  <text x="700" y="820" font-family="Microsoft YaHei" font-size="20" fill="#333333">一个稳定的家庭套餐，在某个时间点，</text>
  <text x="700" y="850" font-family="Microsoft YaHei" font-size="20" fill="#333333">突然新增了一个号码，且该号码的流量，</text>
  <text x="700" y="880" font-family="Microsoft YaHei" font-size="20" fill="#333333">主要消耗在"在线教育"和"游戏"APP上。</text>
  <text x="700" y="920" font-family="Microsoft YaHei" font-size="18" fill="#666666">推断：这是家里的孩子，刚刚拥有了</text>
  <text x="700" y="950" font-family="Microsoft YaHei" font-size="18" fill="#666666">第一部手机</text>
  
  <rect x="1240" y="740" width="520" height="280" fill="#E8F4FD" stroke="#2E5BBA" stroke-width="1"/>
  <text x="1260" y="780" font-family="Microsoft YaHei" font-size="24" font-weight="bold" fill="#2E5BBA">"二胎时代"模式</text>
  <text x="1260" y="820" font-family="Microsoft YaHei" font-size="20" fill="#333333">家庭宽带的夜间流量，在某个时期，</text>
  <text x="1260" y="850" font-family="Microsoft YaHei" font-size="20" fill="#333333">突然激增，且多个终端同时在线。</text>
  <text x="1260" y="890" font-family="Microsoft YaHei" font-size="18" fill="#666666">推断：这很可能是一个新生儿降临，</text>
  <text x="1260" y="920" font-family="Microsoft YaHei" font-size="18" fill="#666666">需要使用"育儿摄像头"等设备的家庭</text>
</svg>
